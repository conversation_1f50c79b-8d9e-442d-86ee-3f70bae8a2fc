{"extension.displayName": "<PERSON><PERSON><PERSON> Code (anteriormente Roo Cline)", "extension.description": "Uma equipe completa de desenvolvimento de agentes de IA no seu editor.", "command.newTask.title": "Nova Tarefa", "command.explainCode.title": "Explicar Código", "command.fixCode.title": "<PERSON><PERSON><PERSON><PERSON>", "command.improveCode.title": "<PERSON><PERSON><PERSON>", "command.addToContext.title": "Adicionar ao Contexto", "command.openInNewTab.title": "Abrir em Nova Aba", "command.focusInput.title": "Focar Campo de Entrada", "command.setCustomStoragePath.title": "Definir Caminho de Armazenamento Personalizado", "command.importSettings.title": "Importar Configurações", "command.terminal.addToContext.title": "Adicionar <PERSON>teúdo do Terminal ao Contexto", "command.terminal.fixCommand.title": "<PERSON><PERSON><PERSON><PERSON>", "command.terminal.explainCommand.title": "Explicar Este Comando", "command.acceptInput.title": "Aceitar Entrada/Sugestão", "views.activitybar.title": "Zhanlu Code", "views.contextMenu.label": "Zhanlu Code", "views.terminalMenu.label": "Zhanlu Code", "views.sidebar.name": "Zhanlu Code", "command.mcpServers.title": "Servidores MCP", "command.prompts.title": "Modos", "command.history.title": "Hist<PERSON><PERSON><PERSON>", "command.marketplace.title": "Marketplace", "command.roomoteAgent.title": "<PERSON><PERSON>", "command.openInEditor.title": "Abrir no Editor", "command.settings.title": "Configurações", "command.documentation.title": "Documentação", "configuration.title": "Zhanlu Code", "commands.allowedCommands.description": "Comandos que podem ser executados automaticamente quando 'Sempre aprovar operações de execução' está ativado", "commands.deniedCommands.description": "Prefixos de comandos que serão automaticamente negados sem solicitar aprovação. Em caso de conflitos com comandos permitidos, a correspondência de prefixo mais longa tem precedência. Adicione * para negar todos os comandos.", "commands.commandExecutionTimeout.description": "Tempo máximo em segundos para aguardar a conclusão da execução do comando antes do timeout (0 = sem timeout, 1-600s, padrão: 0s)", "commands.commandTimeoutAllowlist.description": "Prefixos de comandos que são excluídos do timeout de execução de comandos. Comandos que correspondem a esses prefixos serão executados sem restrições de timeout.", "settings.vsCodeLmModelSelector.description": "Configurações para a API do modelo de linguagem do VSCode", "settings.vsCodeLmModelSelector.vendor.description": "O fornecedor do modelo de linguagem (ex: copilot)", "settings.vsCodeLmModelSelector.family.description": "A família do modelo de linguagem (ex: gpt-4)", "settings.customStoragePath.description": "Caminho de armazenamento personalizado. Deixe vazio para usar o local padrão. Suporta caminhos absolutos (ex: 'D:\\ZhanluStorage')", "settings.enableCodeActions.description": "Habilitar correções rápidas do Zhanlu Code.", "settings.autoImportSettingsPath.description": "Caminho para um arquivo de configuração do Zhanlu para importar automaticamente na inicialização da extensão. Suporta caminhos absolutos e caminhos relativos ao diretório inicial (por exemplo, '~/Documents/zhanlu-settings.json'). Deixe em branco para desativar a importação automática.", "settings.useAgentRules.description": "Habilita o carregamento de arquivos AGENTS.md para regras específicas do agente (consulte https://agent-rules.org/)"}