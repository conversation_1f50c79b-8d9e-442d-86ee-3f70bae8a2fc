{"extension.displayName": "Zhanlu Code (wcześniej Roo Cline)", "extension.description": "Pełny zespół programistów AI w twoim edytorze.", "command.newTask.title": "Nowe Zadanie", "command.explainCode.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "command.fixCode.title": "<PERSON><PERSON><PERSON>", "command.improveCode.title": "Ulepsz Kod", "command.addToContext.title": "Dodaj do Kontekstu", "command.openInNewTab.title": "Otwórz w Nowej Karcie", "command.focusInput.title": "Fokus na Pole Wprowadzania", "command.setCustomStoragePath.title": "Ustaw Niestandardową Ścieżkę Przechowywania", "command.importSettings.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "command.terminal.addToContext.title": "Dodaj <PERSON>ć Terminala do Kontekstu", "command.terminal.fixCommand.title": "Napraw tę <PERSON>", "command.terminal.explainCommand.title": "Wyjaśnij tę Komendę", "command.acceptInput.title": "Akceptuj Wprowadzanie/Sugestię", "views.activitybar.title": "Zhanlu Code", "views.contextMenu.label": "Zhanlu Code", "views.terminalMenu.label": "Zhanlu Code", "views.sidebar.name": "Zhanlu Code", "command.mcpServers.title": "Serwery MCP", "command.prompts.title": "Tryby", "command.history.title": "Historia", "command.marketplace.title": "Marketplace", "command.roomoteAgent.title": "Agent Remote", "command.openInEditor.title": "Otwórz w Edytorze", "command.settings.title": "Ustawienia", "command.documentation.title": "Dokumentacja", "configuration.title": "Zhanlu Code", "commands.allowedCommands.description": "Polecenia, które mogą być wykonywane automatycznie, gdy włączona jest opcja '<PERSON><PERSON><PERSON> zatwierdzaj operacje wykonania'", "commands.deniedCommands.description": "Prefiksy pole<PERSON>ń, kt<PERSON>re będą automatycznie odrzucane bez pytania o zatwierdzenie. W przypadku konfliktów z dozwolonymi poleceniami, najdłuższe dopasowanie prefiksu ma pierwszeństwo. Dodaj * aby odr<PERSON><PERSON>ć wszystkie polecenia.", "commands.commandExecutionTimeout.description": "Maksymalny czas w sekundach oczekiwania na zakończenie wykonania polecenia przed przekroczeniem limitu czasu (0 = brak limitu czasu, 1-600s, domyślnie: 0s)", "commands.commandTimeoutAllowlist.description": "Prefiksy poleceń, które są wykluczone z limitu czasu wykonania poleceń. Polecenia pasujące do tych prefiksów będą wykonywane bez ograniczeń czasowych.", "settings.vsCodeLmModelSelector.description": "Ustawienia dla API modelu językowego VSCode", "settings.vsCodeLmModelSelector.vendor.description": "Dostawca modelu językowego (np. copilot)", "settings.vsCodeLmModelSelector.family.description": "Rodzina modelu językowego (np. gpt-4)", "settings.customStoragePath.description": "Niestandardowa ścieżka przechowywania. Pozostaw puste, aby użyć domyślnej lokalizacji. Obsługuje ścieżki bezwzględne (np. 'D:\\ZhanluStorage')", "settings.enableCodeActions.description": "Włącz szybkie poprawki Zhanlu Code.", "settings.autoImportSettingsPath.description": "Ścieżka do pliku konfiguracyjnego Zhanlu, kt<PERSON>ry ma być automatycznie importowany podczas uruchamiania rozszerzenia. Obsługuje ścieżki bezwzględne i ścieżki względne do katalogu domowego (np. '~/Documents/zhanlu-settings.json'). Pozostaw puste, aby wyłączyć automatyczne importowanie.", "settings.useAgentRules.description": "Włącz wczytywanie plików AGENTS.md dla reguł specyficznych dla agenta (zobacz https://agent-rules.org/)"}