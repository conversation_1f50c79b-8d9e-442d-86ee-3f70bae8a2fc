{"extension.displayName": "Zhanlu Code (ранее Roo Cline)", "extension.description": "Целая команда ИИ-разработчиков в вашем редакторе.", "views.contextMenu.label": "Zhanlu Code", "views.terminalMenu.label": "Zhanlu Code", "views.activitybar.title": "Zhanlu Code", "views.sidebar.name": "Zhanlu Code", "command.newTask.title": "Новая задача", "command.mcpServers.title": "MCP серверы", "command.prompts.title": "Режимы", "command.history.title": "История", "command.marketplace.title": "Маркетплейс", "command.roomoteAgent.title": "Удалённый агент", "command.openInEditor.title": "Открыть в редакторе", "command.settings.title": "Настройки", "command.documentation.title": "Документация", "command.openInNewTab.title": "Открыть в новой вкладке", "command.explainCode.title": "Объяснить код", "command.fixCode.title": "Исправить код", "command.improveCode.title": "Улучшить код", "command.addToContext.title": "Добавить в контекст", "command.focusInput.title": "Фокус на поле ввода", "command.setCustomStoragePath.title": "Указать путь хранения", "command.importSettings.title": "Импортировать настройки", "command.terminal.addToContext.title": "Добавить содержимое терминала в контекст", "command.terminal.fixCommand.title": "Исправить эту команду", "command.terminal.explainCommand.title": "Объяснить эту команду", "command.acceptInput.title": "Принять ввод/предложение", "configuration.title": "Zhanlu Code", "commands.allowedCommands.description": "Команды, которые могут быть автоматически выполнены, когда включена опция 'Всегда подтверждать операции выполнения'", "commands.deniedCommands.description": "Префиксы команд, которые будут автоматически отклонены без запроса подтверждения. В случае конфликтов с разрешенными командами приоритет имеет самое длинное совпадение префикса. Добавьте * чтобы отклонить все команды.", "commands.commandExecutionTimeout.description": "Максимальное время в секундах для ожидания завершения выполнения команды до истечения времени ожидания (0 = без тайм-аута, 1-600с, по умолчанию: 0с)", "commands.commandTimeoutAllowlist.description": "Префиксы команд, которые исключены из тайм-аута выполнения команд. Команды, соответствующие этим префиксам, будут выполняться без ограничений по времени.", "settings.vsCodeLmModelSelector.description": "Настройки для VSCode Language Model API", "settings.vsCodeLmModelSelector.vendor.description": "Поставщик языковой модели (например, copilot)", "settings.vsCodeLmModelSelector.family.description": "Семейство языковой модели (например, gpt-4)", "settings.customStoragePath.description": "Пользовательский путь хранения. Оставьте пустым для использования пути по умолчанию. Поддерживает абсолютные пути (например, 'D:\\ZhanluStorage')", "settings.enableCodeActions.description": "Включить быстрые исправления Zhanlu Code.", "settings.autoImportSettingsPath.description": "Путь к файлу конфигурации Zhanlu для автоматического импорта при запуске расширения. Поддерживает абсолютные пути и пути относительно домашнего каталога (например, '~/Documents/zhanlu-settings.json'). Оставьте пустым, чтобы отключить автоматический импорт.", "settings.useAgentRules.description": "Включить загрузку файлов AGENTS.md для специфичных для агента правил (см. https://agent-rules.org/)"}